// 全局变量存储CodeMirror编辑器实例
let headerEditor, userRulesEditor, existingRulesEditor;

// 防抖函数，避免频繁调用
const debounce = (func, delay) => {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    const addSubscriptionBtn = document.getElementById('add-subscription');
    const subscriptionList = document.getElementById('subscription-list');
    const fetchNodesBtn = document.getElementById('fetch-nodes-btn');
    const nodesResult = document.getElementById('nodes-result');
    const nodesResultContent = document.getElementById('nodes-result-content');

    const saveHeaderBtn = document.getElementById('save-header-btn');
    const headerContent = document.getElementById('header-content');

    const saveRulesBtn = document.getElementById('save-rules-btn');
    const userRules = document.getElementById('user-rules');
    const existingRules = document.getElementById('existing-rules');

    const generateConfigBtn = document.getElementById('generate-config-btn');
    const configUrlContainer = document.getElementById('config-url-container');
    const configUrlInput = document.getElementById('config-url');
    const copyConfigUrlBtn = document.getElementById('copy-config-url-btn');

    const loadingOverlay = document.getElementById('loading-overlay');
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');

    // 定时任务相关元素（稍后获取）
    let autoTaskEnabled, autoTaskTime, autoTaskStatus, nextExecutionTime, lastExecutionTime, saveAutoTaskBtn, runTaskNowBtn, configGenerationTime;

    // 初始化CodeMirror编辑器
    initCodeMirrorEditors();

    // 初始化本地存储
    initLocalStorage();

    // 事件监听器 - 添加订阅
    addSubscriptionBtn.addEventListener('click', () => {
        addSubscriptionRow();
        saveSubscriptions(); // 保存更改
    });

    // 事件监听器 - 移除订阅
    subscriptionList.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-subscription')) {
            const row = e.target.closest('.subscription-row');
            if (subscriptionList.querySelectorAll('.subscription-row').length > 1) {
                row.remove();
                saveSubscriptions(); // 保存更改
            } else {
                showStatus('至少保留一个订阅行', 'warning');
            }
        }
    });

    // 事件监听器 - 获取节点
    fetchNodesBtn.addEventListener('click', async () => {
        await fetchNodes();
    });

    // 事件监听器 - 保存头部配置
    saveHeaderBtn.addEventListener('click', async () => {
        // 保存所有配置到服务器
        const success = await saveAllConfig();
        if (success) {
            showStatus('头部配置已保存', 'success');
        }
    });

    // 事件监听器 - 保存规则配置
    saveRulesBtn.addEventListener('click', async () => {
        // 保存所有配置到服务器
        const success = await saveAllConfig();
        if (success) {
            showStatus('规则配置已保存', 'success');
        }
    });

    // 事件监听器 - 生成配置
    generateConfigBtn.addEventListener('click', async () => {
        await generateConfig();
    });

    // 事件监听器 - 复制配置链接
    copyConfigUrlBtn.addEventListener('click', () => {
        copyToClipboard(configUrlInput.value);
    });

    // 事件监听器 - 监听标签页切换，刷新编辑器
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', event => {
            // 当切换到标签页时强制刷新CodeMirror编辑器
            if (headerEditor) headerEditor.refresh();
            if (userRulesEditor) userRulesEditor.refresh();
            if (existingRulesEditor) existingRulesEditor.refresh();
        });
    });

    // 事件监听器 - 监听订阅列表变化
    subscriptionList.addEventListener('input', (e) => {
        if (e.target.classList.contains('subscription-url')) {
            saveSubscriptions();
        }
    });

    // 事件监听器 - 监听订阅启用状态变化
    subscriptionList.addEventListener('change', (e) => {
        if (e.target.classList.contains('subscription-enabled')) {
            saveSubscriptions();
        }
    });

    // 从本地存储加载数据
    loadFromLocalStorage();

    // 初始化定时任务功能
    initAutoTaskFeature();
});

// 初始化CodeMirror编辑器
function initCodeMirrorEditors() {
    // 配置头部编辑器
    headerEditor = CodeMirror.fromTextArea(document.getElementById('header-content'), {
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        styleActiveLine: true,
        indentUnit: 2,
        tabSize: 2,
        autofocus: false,
        scrollbarStyle: 'simple', // 使用SimpleScrollbars插件
        viewportMargin: 10, // 设置合理的视口边距
        extraKeys: {"Tab": function(cm) {
            if (cm.somethingSelected()) {
                cm.indentSelection("add");
            } else {
                cm.replaceSelection("  ", "end");
            }
        }}
    });

    // 配置用户自定义规则编辑器
    userRulesEditor = CodeMirror.fromTextArea(document.getElementById('user-rules'), {
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        styleActiveLine: true,
        indentUnit: 2,
        tabSize: 2,
        autofocus: false,
        scrollbarStyle: 'simple', // 使用SimpleScrollbars插件
        viewportMargin: 10, // 设置合理的视口边距
        extraKeys: {"Tab": function(cm) {
            if (cm.somethingSelected()) {
                cm.indentSelection("add");
            } else {
                cm.replaceSelection("  ", "end");
            }
        }}
    });

    // 配置已有规则编辑器
    existingRulesEditor = CodeMirror.fromTextArea(document.getElementById('existing-rules'), {
        mode: 'yaml',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        styleActiveLine: true,
        indentUnit: 2,
        tabSize: 2,
        autofocus: false,
        scrollbarStyle: 'simple', // 使用SimpleScrollbars插件
        viewportMargin: 10, // 设置合理的视口边距
        extraKeys: {"Tab": function(cm) {
            if (cm.somethingSelected()) {
                cm.indentSelection("add");
            } else {
                cm.replaceSelection("  ", "end");
            }
        }}
    });

    // 添加编辑器内容变化时的自动保存功能
    // 使用防抖函数，避免频繁保存

    // 头部编辑器自动保存
    headerEditor.on('change', debounce(() => {
        saveAllConfig();
    }, 2000)); // 2秒后保存

    // 用户自定义规则编辑器自动保存
    userRulesEditor.on('change', debounce(() => {
        saveAllConfig();
    }, 2000)); // 2秒后保存

    // 已有规则编辑器自动保存
    existingRulesEditor.on('change', debounce(() => {
        saveAllConfig();
    }, 2000)); // 2秒后保存
}

// 初始化配置
function initLocalStorage() {
    // 这个函数现在只是为了兼容性保留，实际上我们从服务器加载配置
    console.log('正在从服务器加载配置...');
}

// 从服务器加载配置数据
async function loadFromLocalStorage() {
    try {
        showLoading(true);
        console.log('开始从服务器加载配置...');

        // 检查是否存在已生成的配置文件
        try {
            const configCheckResponse = await fetch('/api/check-config-exists');
            const configCheckData = await configCheckResponse.json();

            console.log('配置文件检查结果:', configCheckData);

            if (configCheckData.exists) {
                // 如果配置文件存在，显示配置链接
                document.getElementById('config-url').value = configCheckData.configUrl;

                // 显示上次修改时间
                if (configCheckData.lastModified) {
                    const lastModified = new Date(configCheckData.lastModified);
                    console.log(`配置文件最后修改时间: ${lastModified.toLocaleString()}`);
                    showStatus(`已找到现有配置文件 (上次修改: ${lastModified.toLocaleString()})`, 'info', 5000);

                    // 更新配置生成时间显示
                    if (configGenerationTime) {
                        const timeString = lastModified.toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                        configGenerationTime.textContent = `生成时间: ${timeString}`;
                    }
                }
            } else {
                // 如果配置文件不存在，清空配置链接
                document.getElementById('config-url').value = '';
                console.log('未找到现有配置文件');
            }
        } catch (configCheckError) {
            console.error('检查配置文件失败:', configCheckError);
        }

        // 从服务器获取配置
        try {
            const response = await fetch('/api/load-config');
            console.log(`服务器响应状态: ${response.status}`);

            // 尝试解析响应
            let config;
            try {
                config = await response.json();
                console.log('服务器响应数据:', config);

                if (config.error) {
                    console.warn('服务器返回错误:', config.error);
                    showStatus(`加载配置警告: ${config.error}`, 'warning');
                }

                if (config.message) {
                    console.log('服务器消息:', config.message);
                }

                // 加载订阅
                const subscriptionList = document.getElementById('subscription-list');

                // 清空现有订阅行
                subscriptionList.innerHTML = '';

                if (!config.subscriptions || config.subscriptions.length === 0) {
                    console.log('没有找到订阅配置，添加默认空行');
                    // 如果没有保存的订阅，添加两个空行
                    addSubscriptionRow();
                    addSubscriptionRow();
                } else {
                    console.log(`加载 ${config.subscriptions.length} 个订阅`);
                    // 添加保存的订阅
                    config.subscriptions.forEach(sub => {
                        addSubscriptionRow(sub.url, sub.enabled);
                    });
                }

                // 加载头部配置和规则配置到CodeMirror编辑器
                console.log('设置编辑器内容...');
                headerEditor.setValue(config.header || '');
                userRulesEditor.setValue(config.userRules || '');
                existingRulesEditor.setValue(config.existingRules || '');

                // 显示上次更新时间
                if (config.lastUpdated) {
                    const lastUpdated = new Date(config.lastUpdated);
                    console.log(`配置最后更新时间: ${lastUpdated.toLocaleString()}`);
                    showStatus(`配置已加载 (上次更新: ${lastUpdated.toLocaleString()})`, 'success');
                } else {
                    console.log('没有找到上次更新时间，使用默认配置');
                    showStatus('使用默认配置', 'info');
                }

                // 保存一次配置，确保配置文件存在
                if (!config.lastUpdated) {
                    console.log('首次加载，保存默认配置到服务器');
                    setTimeout(() => {
                        saveAllConfig().then(success => {
                            if (success) {
                                console.log('默认配置已保存到服务器');
                            }
                        });
                    }, 2000);
                }
            } catch (jsonError) {
                console.error('解析响应JSON失败:', jsonError);
                const responseText = await response.text();
                console.log('原始响应内容:', responseText);
                throw new Error(`解析服务器响应失败: ${jsonError.message}`);
            }

            if (!response.ok && !config.subscriptions) {
                throw new Error(`服务器错误: ${response.status}`);
            }
        } catch (fetchError) {
            console.error('获取配置失败:', fetchError);
            showStatus(`加载配置失败: ${fetchError.message}`, 'danger');

            // 如果加载失败，添加两个空行
            const subscriptionList = document.getElementById('subscription-list');
            subscriptionList.innerHTML = '';
            addSubscriptionRow();
            addSubscriptionRow();

            // 设置空编辑器内容
            headerEditor.setValue('');
            userRulesEditor.setValue('');
            existingRulesEditor.setValue('');
        }
    } catch (error) {
        console.error('加载配置失败:', error);
        showStatus(`加载配置失败: ${error.message}`, 'danger');

        // 如果加载失败，添加两个空行
        const subscriptionList = document.getElementById('subscription-list');
        subscriptionList.innerHTML = '';
        addSubscriptionRow();
        addSubscriptionRow();

        // 设置空编辑器内容
        headerEditor.setValue('');
        userRulesEditor.setValue('');
        existingRulesEditor.setValue('');
    } finally {
        showLoading(false);
    }
}

// 添加订阅行
function addSubscriptionRow(url = '', enabled = true) {
    const subscriptionList = document.getElementById('subscription-list');
    const row = document.createElement('div');
    row.className = 'subscription-row mb-3';

    row.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control subscription-url" placeholder="Clash订阅链接" value="${url}">
            <div class="input-group-text">
                <div class="form-check form-switch">
                    <input class="form-check-input subscription-enabled" type="checkbox" ${enabled ? 'checked' : ''}>
                    <label class="form-check-label">启用</label>
                </div>
            </div>
            <button class="btn btn-outline-danger remove-subscription" type="button">删除</button>
        </div>
    `;

    subscriptionList.appendChild(row);
}

// 保存所有配置到服务器
async function saveAllConfig() {
    try {
        const subscriptions = getAllSubscriptions();
        const header = headerEditor.getValue();
        const userRules = userRulesEditor.getValue();
        const existingRules = existingRulesEditor.getValue();

        console.log('准备保存配置到服务器...');
        console.log(`订阅数量: ${subscriptions.length}`);
        console.log(`头部配置长度: ${header.length} 字符`);
        console.log(`用户规则长度: ${userRules.length} 字符`);
        console.log(`已有规则长度: ${existingRules.length} 字符`);

        // 发送到服务器
        try {
            const response = await fetch('/api/save-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    subscriptions,
                    header,
                    userRules,
                    existingRules
                })
            });

            console.log(`服务器响应状态: ${response.status}`);

            // 尝试解析响应
            let responseData;
            try {
                responseData = await response.json();
                console.log('服务器响应数据:', responseData);
            } catch (jsonError) {
                console.error('解析响应JSON失败:', jsonError);
                const responseText = await response.text();
                console.log('原始响应内容:', responseText);
                throw new Error(`解析服务器响应失败: ${jsonError.message}`);
            }

            if (!response.ok) {
                throw new Error(responseData.error || `服务器错误: ${response.status}`);
            }

            // 显示成功消息，但不要打断用户操作
            showStatus('配置已自动保存', 'success');

            return true;
        } catch (fetchError) {
            console.error('发送请求失败:', fetchError);
            showStatus(`保存配置失败: ${fetchError.message}`, 'danger');
            return false;
        }
    } catch (error) {
        console.error('保存配置失败:', error);
        showStatus(`保存配置失败: ${error.message}`, 'danger');
        return false;
    }
}

// 获取所有订阅信息（不保存）
function getAllSubscriptions() {
    const subscriptionRows = document.querySelectorAll('.subscription-row');
    const subscriptions = [];

    subscriptionRows.forEach(row => {
        const url = row.querySelector('.subscription-url').value.trim();
        const enabled = row.querySelector('.subscription-enabled').checked;

        // 保存所有订阅，即使URL为空
        subscriptions.push({ url, enabled });
    });

    return subscriptions;
}

// 保存所有订阅到服务器（带防抖）
const saveSubscriptions = debounce(async () => {
    await saveAllConfig();
}, 1000);

// 获取当前启用的订阅
function getEnabledSubscriptions() {
    const subscriptions = getAllSubscriptions();

    // 返回启用的且URL不为空的订阅URL
    return subscriptions.filter(sub => sub.enabled && sub.url).map(sub => sub.url);
}

// 获取节点
async function fetchNodes() {
    const enabledSubscriptions = getEnabledSubscriptions();

    if (enabledSubscriptions.length === 0) {
        showStatus('没有启用的订阅', 'warning');
        return;
    }

    showLoading(true);

    try {
        const response = await fetch('/api/fetch-nodes', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ urls: enabledSubscriptions })
        });

        const data = await response.json();

        if (response.ok) {
            // 显示节点获取结果
            displayNodesFetchResult(data.results);
        } else {
            showStatus(`获取节点失败: ${data.error}`, 'danger');
        }
    } catch (error) {
        console.error('获取节点错误:', error);
        showStatus(`获取节点错误: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
    }
}

// 显示节点获取结果
function displayNodesFetchResult(results) {
    const nodesResult = document.getElementById('nodes-result');
    const nodesResultContent = document.getElementById('nodes-result-content');

    let htmlContent = '';
    let totalSuccess = 0;
    let totalFailed = 0;

    results.forEach(result => {
        if (result.success) {
            totalSuccess++;
            htmlContent += `<p>✅ 成功获取订阅 [${result.url}]: ${result.proxies.length} 个节点</p>`;
        } else {
            totalFailed++;
            htmlContent += `<p>❌ 获取订阅失败 [${result.url}]: ${result.error}</p>`;
        }
    });

    htmlContent += `<p><strong>总结:</strong> 成功 ${totalSuccess} 个订阅, 失败 ${totalFailed} 个订阅</p>`;

    nodesResultContent.innerHTML = htmlContent;
    nodesResult.classList.remove('d-none');

    if (totalSuccess > 0) {
        showStatus(`成功获取 ${totalSuccess} 个订阅的节点`, 'success');
    } else {
        showStatus('所有订阅获取失败', 'danger');
    }
}

// 生成配置
async function generateConfig() {
    const enabledSubscriptions = getEnabledSubscriptions();

    if (enabledSubscriptions.length === 0) {
        showStatus('没有启用的订阅', 'warning');
        return;
    }

    // 从CodeMirror编辑器获取当前内容并保存到服务器
    const header = headerEditor.getValue();
    const userRulesContent = userRulesEditor.getValue();
    const existingRulesContent = existingRulesEditor.getValue();

    // 保存所有配置到服务器
    await saveAllConfig();

    const rules = {
        userCustomRules: userRulesContent,
        existingRules: existingRulesContent
    };

    showLoading(true);

    try {
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                header,
                rules,
                enabledSubscriptions
            })
        });

        // 检查HTTP状态
        if (!response.ok) {
            // 尝试解析错误消息
            let errorMessage;
            try {
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.indexOf("application/json") !== -1) {
                    const errorData = await response.json();
                    errorMessage = errorData.error || `服务器错误: ${response.status}`;
                } else {
                    // 非JSON响应
                    const text = await response.text();
                    errorMessage = `服务器错误 (${response.status}): 非JSON响应`;
                    console.error('服务器返回非JSON响应:', text);
                }
            } catch (parseError) {
                errorMessage = `服务器错误 (${response.status}): 无法解析响应`;
                console.error('解析响应错误:', parseError);
            }

            showStatus(`生成配置失败: ${errorMessage}`, 'danger');
            showLoading(false);
            return;
        }

        // 正常解析JSON响应
        let data;
        try {
            data = await response.json();
        } catch (jsonError) {
            console.error('解析JSON响应错误:', jsonError);
            showStatus('生成配置失败: 解析服务器响应出错', 'danger');
            showLoading(false);
            return;
        }

        // 显示配置链接
        const configUrl = `${window.location.origin}/api/config/${data.configPath}`;
        document.getElementById('config-url').value = configUrl;
        document.getElementById('config-url-container').classList.remove('d-none');

        // 更新配置生成时间
        updateConfigGenerationTime();

        showStatus('配置文件生成成功', 'success');
    } catch (error) {
        console.error('生成配置错误:', error);
        showStatus(`生成配置错误: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    // 创建一个临时的textarea元素
    const textarea = document.createElement('textarea');
    textarea.value = text;

    // 设置样式使其不可见
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';

    // 添加到DOM
    document.body.appendChild(textarea);

    // 选择文本
    textarea.select();
    textarea.setSelectionRange(0, 99999); // 兼容移动设备

    try {
        // 执行复制命令
        const successful = document.execCommand('copy');
        if (successful) {
            showStatus('已复制到剪贴板', 'success');
        } else {
            throw new Error('复制命令执行失败');
        }
    } catch (err) {
        console.error('复制失败:', err);

        // 如果execCommand失败，尝试使用Clipboard API
        try {
            navigator.clipboard.writeText(text)
                .then(() => {
                    showStatus('已复制到剪贴板', 'success');
                })
                .catch(clipErr => {
                    console.error('Clipboard API失败:', clipErr);
                    showStatus('复制失败，请手动复制', 'warning');

                    // 选中输入框中的文本，方便用户手动复制
                    document.getElementById('config-url').focus();
                    document.getElementById('config-url').select();
                });
        } catch (clipErr) {
            console.error('Clipboard API不可用:', clipErr);
            showStatus('复制失败，请手动复制', 'warning');

            // 选中输入框中的文本，方便用户手动复制
            document.getElementById('config-url').focus();
            document.getElementById('config-url').select();
        }
    } finally {
        // 清理
        document.body.removeChild(textarea);
    }
}

// 显示/隐藏加载中遮罩
function showLoading(show) {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (show) {
        loadingOverlay.classList.remove('d-none');
    } else {
        loadingOverlay.classList.add('d-none');
    }
}

// 显示状态消息
function showStatus(message, type = 'info', duration = 3000) {
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');
    const alertElement = statusContainer.querySelector('.alert');

    // 设置消息
    statusMessage.textContent = message;

    // 设置类型
    alertElement.className = `alert alert-${type}`;

    // 显示容器
    statusContainer.classList.remove('d-none');

    // 指定时间后自动隐藏
    setTimeout(() => {
        statusContainer.classList.add('d-none');
    }, duration);
}

// 初始化文件系统检查功能
document.addEventListener('DOMContentLoaded', () => {
    // 事件监听器 - 检查文件系统
    const checkFilesystemBtn = document.getElementById('check-filesystem-btn');
    const filesystemResult = document.getElementById('filesystem-result');
    const filesystemResultContent = document.getElementById('filesystem-result-content');

    if (checkFilesystemBtn) {
        checkFilesystemBtn.addEventListener('click', async () => {
            try {
                showLoading(true);
                checkFilesystemBtn.disabled = true;

                const response = await fetch('/api/check-filesystem');
                const data = await response.json();

                // 显示结果
                filesystemResult.classList.remove('d-none');

                // 格式化结果
                let resultHtml = '';

                // 添加摘要
                if (data.summary) {
                    const alertClass = data.summary.success ? 'alert-success' : 'alert-danger';
                    resultHtml += `<div class="alert ${alertClass} p-2 mb-2">${data.summary.message}</div>`;
                }

                // 添加检查结果
                if (data.checks && data.checks.length > 0) {
                    resultHtml += '<table class="table table-sm table-bordered">';
                    resultHtml += '<thead><tr><th>检查项</th><th>结果</th><th>路径</th><th>消息</th></tr></thead>';
                    resultHtml += '<tbody>';

                    data.checks.forEach(check => {
                        const rowClass = check.success ? 'table-success' : 'table-danger';
                        const statusIcon = check.success ? '✅' : '❌';

                        resultHtml += `<tr class="${rowClass}">`;
                        resultHtml += `<td>${check.name}</td>`;
                        resultHtml += `<td>${statusIcon}</td>`;
                        resultHtml += `<td class="text-break"><small>${check.path}</small></td>`;
                        resultHtml += `<td>${check.message}</td>`;
                        resultHtml += '</tr>';
                    });

                    resultHtml += '</tbody></table>';
                }

                // 添加进程信息
                if (data.process) {
                    resultHtml += '<h6 class="mt-3">进程信息:</h6>';
                    resultHtml += '<table class="table table-sm">';
                    resultHtml += `<tr><td>PID</td><td>${data.process.pid}</td></tr>`;
                    resultHtml += `<tr><td>平台</td><td>${data.process.platform}</td></tr>`;
                    resultHtml += `<tr><td>Node版本</td><td>${data.process.version}</td></tr>`;
                    resultHtml += `<tr><td>工作目录</td><td>${data.process.cwd}</td></tr>`;

                    if (data.process.uid !== 'N/A') {
                        resultHtml += `<tr><td>UID</td><td>${data.process.uid}</td></tr>`;
                    }

                    if (data.process.gid !== 'N/A') {
                        resultHtml += `<tr><td>GID</td><td>${data.process.gid}</td></tr>`;
                    }

                    resultHtml += '</table>';
                }

                filesystemResultContent.innerHTML = resultHtml;

                // 显示状态
                if (data.summary && data.summary.success) {
                    showStatus('文件系统检查完成: 一切正常', 'success');
                } else {
                    showStatus('文件系统检查完成: 发现问题', 'warning');
                }
            } catch (error) {
                console.error('检查文件系统失败:', error);
                showStatus(`检查文件系统失败: ${error.message}`, 'danger');

                filesystemResult.classList.remove('d-none');
                filesystemResultContent.innerHTML = `<div class="alert alert-danger">检查失败: ${error.message}</div>`;
            } finally {
                showLoading(false);
                checkFilesystemBtn.disabled = false;
            }
        });
    }
});

// 初始化定时任务功能
function initAutoTaskFeature() {
    // 获取定时任务相关元素
    autoTaskEnabled = document.getElementById('auto-task-enabled');
    autoTaskTime = document.getElementById('auto-task-time');
    autoTaskStatus = document.getElementById('auto-task-status');
    nextExecutionTime = document.getElementById('next-execution-time');
    lastExecutionTime = document.getElementById('last-execution-time');
    saveAutoTaskBtn = document.getElementById('save-auto-task-btn');
    runTaskNowBtn = document.getElementById('run-task-now-btn');
    configGenerationTime = document.getElementById('config-generation-time');

    // 检查元素是否存在
    if (!autoTaskEnabled || !autoTaskTime || !autoTaskStatus || !nextExecutionTime ||
        !lastExecutionTime || !saveAutoTaskBtn || !runTaskNowBtn || !configGenerationTime) {
        console.error('定时任务相关元素未找到，请检查HTML结构');
        return;
    }

    // 绑定事件监听器
    saveAutoTaskBtn.addEventListener('click', saveAutoTaskConfig);
    runTaskNowBtn.addEventListener('click', runTaskNow);

    // 加载定时任务状态
    loadAutoTaskStatus();

    console.log('定时任务功能初始化完成');
}

// 定时任务相关函数
async function loadAutoTaskStatus() {
    try {
        // 检查元素是否存在
        if (!autoTaskEnabled || !autoTaskTime || !autoTaskStatus || !nextExecutionTime || !lastExecutionTime) {
            console.warn('定时任务相关元素未初始化，跳过状态加载');
            return;
        }

        const response = await fetch('/api/auto-task/status');
        const data = await response.json();

        // 更新UI
        autoTaskEnabled.checked = data.enabled;
        autoTaskTime.value = data.time;
        autoTaskStatus.textContent = data.enabled ? (data.isRunning ? '运行中' : '已启用') : '未启用';
        nextExecutionTime.textContent = data.nextExecutionFormatted || '-';
        lastExecutionTime.textContent = data.lastExecutionFormatted || '-';

        console.log('定时任务状态已加载:', data);
    } catch (error) {
        console.error('加载定时任务状态失败:', error);
        showStatus('加载定时任务状态失败', 'warning');
    }
}

async function saveAutoTaskConfig() {
    try {
        // 检查元素是否存在
        if (!autoTaskEnabled || !autoTaskTime || !saveAutoTaskBtn) {
            console.error('定时任务相关元素未初始化');
            showStatus('定时任务功能未初始化', 'danger');
            return;
        }

        showLoading(true);
        saveAutoTaskBtn.disabled = true;

        const enabled = autoTaskEnabled.checked;
        const time = autoTaskTime.value;

        if (!time) {
            showStatus('请设置执行时间', 'warning');
            return;
        }

        const response = await fetch('/api/auto-task/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ enabled, time })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '保存失败');
        }

        showStatus(data.message, 'success');

        // 重新加载状态
        await loadAutoTaskStatus();

    } catch (error) {
        console.error('保存定时任务配置失败:', error);
        showStatus(`保存失败: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
        saveAutoTaskBtn.disabled = false;
    }
}

async function runTaskNow() {
    try {
        // 检查元素是否存在
        if (!runTaskNowBtn) {
            console.error('定时任务相关元素未初始化');
            showStatus('定时任务功能未初始化', 'danger');
            return;
        }

        showLoading(true);
        runTaskNowBtn.disabled = true;

        showStatus('正在执行定时任务...', 'info');

        const response = await fetch('/api/auto-task/run-now', {
            method: 'POST'
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '执行失败');
        }

        if (data.success) {
            showStatus('定时任务执行成功，配置已生成', 'success');

            // 重新检查配置文件
            const configCheckResponse = await fetch('/api/check-config-exists');
            const configCheckData = await configCheckResponse.json();

            if (configCheckData.exists) {
                document.getElementById('config-url').value = configCheckData.configUrl;
                updateConfigGenerationTime();
            }
        } else {
            showStatus(`执行失败: ${data.message}`, 'warning');
        }

        // 重新加载状态
        await loadAutoTaskStatus();

    } catch (error) {
        console.error('立即执行定时任务失败:', error);
        showStatus(`执行失败: ${error.message}`, 'danger');
    } finally {
        showLoading(false);
        runTaskNowBtn.disabled = false;
    }
}

// 更新配置生成时间显示
function updateConfigGenerationTime() {
    try {
        // 检查元素是否存在
        if (!configGenerationTime) {
            console.warn('配置生成时间元素未初始化，跳过更新');
            return;
        }

        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        configGenerationTime.textContent = `生成时间: ${timeString}`;
    } catch (error) {
        console.error('更新配置生成时间失败:', error);
    }
}

// ==================== 自定义节点功能 ====================

// 初始化自定义节点功能
function initCustomNodesFeature() {
    console.log('开始初始化自定义节点功能...');

    // 添加自定义节点按钮事件监听器
    document.querySelectorAll('.dropdown-item[data-node-type]').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const nodeType = e.target.closest('[data-node-type]').getAttribute('data-node-type');
            console.log(`添加 ${nodeType} 节点`);
            addCustomNode(nodeType);
        });
    });

    // 保存自定义节点按钮事件监听器
    const saveCustomNodesBtn = document.getElementById('save-custom-nodes-btn');
    if (saveCustomNodesBtn) {
        saveCustomNodesBtn.addEventListener('click', async () => {
            const success = await saveAllConfig();
            if (success) {
                showStatus('自定义节点已保存', 'success');
                updateCustomNodesResult();
            }
        });
    }

    // 渲染现有的自定义节点
    renderCustomNodes();

    console.log('自定义节点功能初始化完成');
}

// 添加自定义节点
function addCustomNode(type) {
    const nodeId = Date.now().toString();
    const node = {
        id: nodeId,
        type: type,
        enabled: true,
        customName: '',
        config: getDefaultNodeConfig(type)
    };

    customNodes.push(node);
    console.log(`添加了新的 ${type} 节点:`, node);
    renderCustomNodes();
    saveAllConfig(); // 自动保存
}

// 获取默认节点配置
function getDefaultNodeConfig(type) {
    switch (type) {
        case 'trojan':
            return {
                server: '',
                port: 443,
                password: '',
                sni: '',
                'skip-cert-verify': false,
                udp: true
            };
        case 'ss':
            return {
                server: '',
                port: 443,
                cipher: 'aes-256-gcm',
                password: '',
                udp: true
            };
        case 'vmess':
            return {
                server: '',
                port: 443,
                uuid: '',
                alterId: 0,
                cipher: 'auto',
                network: 'tcp',
                tls: true,
                'skip-cert-verify': false,
                udp: true,
                'ws-opts': {
                    path: '/',
                    headers: {
                        Host: ''
                    }
                }
            };
        default:
            return {};
    }
}

// 渲染自定义节点列表
function renderCustomNodes() {
    const container = document.getElementById('custom-nodes-list');
    if (!container) {
        console.warn('自定义节点容器未找到');
        return;
    }

    console.log(`渲染 ${customNodes.length} 个自定义节点`);
    container.innerHTML = '';

    customNodes.forEach((node, index) => {
        const nodeElement = createCustomNodeElement(node, index);
        container.appendChild(nodeElement);
    });

    updateCustomNodesResult();
}

// 创建自定义节点元素
function createCustomNodeElement(node, index) {
    const div = document.createElement('div');
    div.className = 'custom-node-item border rounded p-3 mb-3';
    div.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
                <i class="bi bi-${getNodeIcon(node.type)}"></i>
                ${getNodeTypeName(node.type)} 节点 #${index + 1}
            </h6>
            <div class="d-flex align-items-center gap-2">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="node-enabled-${node.id}"
                           ${node.enabled ? 'checked' : ''} onchange="toggleCustomNode('${node.id}')">
                    <label class="form-check-label" for="node-enabled-${node.id}">启用</label>
                </div>
                <button class="btn btn-outline-danger btn-sm" onclick="removeCustomNode('${node.id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label">自定义名称 (可选)</label>
                <input type="text" class="form-control" placeholder="留空则自动生成"
                       value="${node.customName || ''}" onchange="updateCustomNodeName('${node.id}', this.value)">
            </div>
        </div>

        ${createNodeConfigForm(node)}
    `;
    return div;
}

// 获取节点图标
function getNodeIcon(type) {
    switch (type) {
        case 'trojan': return 'shield-lock';
        case 'ss': return 'eye-slash';
        case 'vmess': return 'lightning';
        default: return 'gear';
    }
}

// 获取节点类型名称
function getNodeTypeName(type) {
    switch (type) {
        case 'trojan': return 'Trojan';
        case 'ss': return 'Shadowsocks';
        case 'vmess': return 'VMess';
        default: return '未知';
    }
}

// 切换自定义节点启用状态
function toggleCustomNode(nodeId) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node) {
        node.enabled = !node.enabled;
        saveAllConfig(); // 自动保存
        updateCustomNodesResult();
    }
}

// 移除自定义节点
function removeCustomNode(nodeId) {
    const index = customNodes.findIndex(n => n.id === nodeId);
    if (index !== -1) {
        customNodes.splice(index, 1);
        renderCustomNodes();
        saveAllConfig(); // 自动保存
    }
}

// 更新自定义节点名称
function updateCustomNodeName(nodeId, name) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node) {
        node.customName = name.trim();
        saveAllConfig(); // 自动保存
        updateCustomNodesResult();
    }
}

// 更新节点配置
function updateNodeConfig(nodeId, key, value) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node) {
        node.config[key] = value;
        saveAllConfig(); // 自动保存

        // 如果是VMess节点且网络类型改变，重新渲染
        if (node.type === 'vmess' && key === 'network') {
            renderCustomNodes();
        }
    }
}

// 更新自定义节点结果显示
function updateCustomNodesResult() {
    const resultContainer = document.getElementById('custom-nodes-result');
    const resultContent = document.getElementById('custom-nodes-result-content');

    if (!resultContainer || !resultContent) return;

    const enabledNodes = customNodes.filter(node => node.enabled);
    const totalNodes = customNodes.length;

    if (totalNodes === 0) {
        resultContainer.classList.add('d-none');
        return;
    }

    resultContainer.classList.remove('d-none');
    resultContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>总节点数:</strong> ${totalNodes}
            </div>
            <div class="col-md-6">
                <strong>启用节点数:</strong> ${enabledNodes.length}
            </div>
        </div>
    `;
}

// 创建节点配置表单
function createNodeConfigForm(node) {
    switch (node.type) {
        case 'trojan':
            return `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">服务器地址 *</label>
                        <input type="text" class="form-control" value="${node.config.server || ''}"
                               onchange="updateNodeConfig('${node.id}', 'server', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">端口 *</label>
                        <input type="number" class="form-control" value="${node.config.port || 443}"
                               onchange="updateNodeConfig('${node.id}', 'port', parseInt(this.value))" min="1" max="65535">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">密码 *</label>
                        <input type="text" class="form-control" value="${node.config.password || ''}"
                               onchange="updateNodeConfig('${node.id}', 'password', this.value)" placeholder="trojan密码">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">SNI (可选)</label>
                        <input type="text" class="form-control" value="${node.config.sni || ''}"
                               onchange="updateNodeConfig('${node.id}', 'sni', this.value)" placeholder="域名">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip-cert-${node.id}"
                                   ${node.config['skip-cert-verify'] ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'skip-cert-verify', this.checked)">
                            <label class="form-check-label" for="skip-cert-${node.id}">跳过证书验证</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="udp-${node.id}"
                                   ${node.config.udp ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'udp', this.checked)">
                            <label class="form-check-label" for="udp-${node.id}">UDP 支持</label>
                        </div>
                    </div>
                </div>
            `;
        case 'ss':
            return `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">服务器地址 *</label>
                        <input type="text" class="form-control" value="${node.config.server || ''}"
                               onchange="updateNodeConfig('${node.id}', 'server', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">端口 *</label>
                        <input type="number" class="form-control" value="${node.config.port || 443}"
                               onchange="updateNodeConfig('${node.id}', 'port', parseInt(this.value))" min="1" max="65535">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">加密方式 *</label>
                        <select class="form-select" onchange="updateNodeConfig('${node.id}', 'cipher', this.value)">
                            <option value="aes-256-gcm" ${node.config.cipher === 'aes-256-gcm' ? 'selected' : ''}>aes-256-gcm</option>
                            <option value="aes-128-gcm" ${node.config.cipher === 'aes-128-gcm' ? 'selected' : ''}>aes-128-gcm</option>
                            <option value="chacha20-ietf-poly1305" ${node.config.cipher === 'chacha20-ietf-poly1305' ? 'selected' : ''}>chacha20-ietf-poly1305</option>
                            <option value="xchacha20-ietf-poly1305" ${node.config.cipher === 'xchacha20-ietf-poly1305' ? 'selected' : ''}>xchacha20-ietf-poly1305</option>
                            <option value="aes-256-cfb" ${node.config.cipher === 'aes-256-cfb' ? 'selected' : ''}>aes-256-cfb</option>
                            <option value="aes-128-cfb" ${node.config.cipher === 'aes-128-cfb' ? 'selected' : ''}>aes-128-cfb</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">密码 *</label>
                        <input type="text" class="form-control" value="${node.config.password || ''}"
                               onchange="updateNodeConfig('${node.id}', 'password', this.value)" placeholder="SS密码">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="udp-${node.id}"
                                   ${node.config.udp ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'udp', this.checked)">
                            <label class="form-check-label" for="udp-${node.id}">UDP 支持</label>
                        </div>
                    </div>
                </div>
            `;
        case 'vmess':
            return `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">服务器地址 *</label>
                        <input type="text" class="form-control" value="${node.config.server || ''}"
                               onchange="updateNodeConfig('${node.id}', 'server', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">端口 *</label>
                        <input type="number" class="form-control" value="${node.config.port || 443}"
                               onchange="updateNodeConfig('${node.id}', 'port', parseInt(this.value))" min="1" max="65535">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">UUID *</label>
                        <input type="text" class="form-control" value="${node.config.uuid || ''}"
                               onchange="updateNodeConfig('${node.id}', 'uuid', this.value)" placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Alter ID</label>
                        <input type="number" class="form-control" value="${node.config.alterId || 0}"
                               onchange="updateNodeConfig('${node.id}', 'alterId', parseInt(this.value))" min="0" max="255">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">加密方式</label>
                        <select class="form-select" onchange="updateNodeConfig('${node.id}', 'cipher', this.value)">
                            <option value="auto" ${node.config.cipher === 'auto' ? 'selected' : ''}>auto</option>
                            <option value="aes-128-gcm" ${node.config.cipher === 'aes-128-gcm' ? 'selected' : ''}>aes-128-gcm</option>
                            <option value="chacha20-poly1305" ${node.config.cipher === 'chacha20-poly1305' ? 'selected' : ''}>chacha20-poly1305</option>
                            <option value="none" ${node.config.cipher === 'none' ? 'selected' : ''}>none</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">网络类型</label>
                        <select class="form-select" onchange="updateNodeConfig('${node.id}', 'network', this.value)">
                            <option value="tcp" ${node.config.network === 'tcp' ? 'selected' : ''}>TCP</option>
                            <option value="ws" ${node.config.network === 'ws' ? 'selected' : ''}>WebSocket</option>
                            <option value="h2" ${node.config.network === 'h2' ? 'selected' : ''}>HTTP/2</option>
                            <option value="grpc" ${node.config.network === 'grpc' ? 'selected' : ''}>gRPC</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="tls-${node.id}"
                                   ${node.config.tls ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'tls', this.checked)">
                            <label class="form-check-label" for="tls-${node.id}">启用 TLS</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip-cert-${node.id}"
                                   ${node.config['skip-cert-verify'] ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'skip-cert-verify', this.checked)">
                            <label class="form-check-label" for="skip-cert-${node.id}">跳过证书验证</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="udp-${node.id}"
                                   ${node.config.udp ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'udp', this.checked)">
                            <label class="form-check-label" for="udp-${node.id}">UDP 支持</label>
                        </div>
                    </div>
                    ${node.config.network === 'ws' ? `
                    <div class="col-md-6 mb-3">
                        <label class="form-label">WebSocket 路径</label>
                        <input type="text" class="form-control" value="${node.config['ws-opts']?.path || ''}"
                               onchange="updateNodeWSConfig('${node.id}', 'path', this.value)" placeholder="/path">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">WebSocket Host</label>
                        <input type="text" class="form-control" value="${node.config['ws-opts']?.headers?.Host || ''}"
                               onchange="updateNodeWSConfig('${node.id}', 'host', this.value)" placeholder="example.com">
                    </div>
                    ` : ''}
                </div>
            `;
        default:
            return '<p class="text-muted">不支持的节点类型</p>';
    }
}

// 更新VMess WebSocket配置
function updateNodeWSConfig(nodeId, key, value) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node && node.type === 'vmess') {
        if (!node.config['ws-opts']) {
            node.config['ws-opts'] = { path: '/', headers: { Host: '' } };
        }

        if (key === 'path') {
            node.config['ws-opts'].path = value;
        } else if (key === 'host') {
            if (!node.config['ws-opts'].headers) {
                node.config['ws-opts'].headers = {};
            }
            node.config['ws-opts'].headers.Host = value;
        }

        saveAllConfig(); // 自动保存
    }
}